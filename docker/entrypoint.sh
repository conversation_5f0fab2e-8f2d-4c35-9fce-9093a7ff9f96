#!/bin/bash
set -e

case ${1} in
  app:start)
    # 检查并创建 keys 文件夹
    if [ ! -d "/opt/htdocs/storage/keys" ]; then
      mkdir -p /opt/htdocs/storage/keys
      # 设置用户权限
      chown -R www-data:www-data /opt/htdocs/storage/keys
    fi

    rm -rf /var/www/.ssh/keys
    ln -sf /opt/htdocs/storage/keys /var/www/.ssh/keys

    rm -rf /var/run/supervisor.sock
    exec /usr/bin/supervisord -nc /etc/supervisor/supervisord.conf
    ;;
  app:init)
    php think migrate:run
    ;;
  *)
    exec "$@"
    ;;
esac
