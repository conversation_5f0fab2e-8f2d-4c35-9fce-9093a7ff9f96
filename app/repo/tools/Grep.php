<?php

namespace app\repo\tools;

use app\repo\Workspace;
use Symfony\Component\Process\Process;
use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Error;
use think\agent\tool\result\Plain;

class Grep extends FunctionCall
{
    protected $title = 'Grep';

    protected $name = 'grep';

    protected $description = <<<EOT
A powerful search tool built on ripgrep
Usage:
    - ALWAYS use Grep for search tasks. .
    - Supports full regex syntax (e.g., \"log.*Error\", \"function\\s+\\w+\")
    - Filter files with glob parameter (e.g., \"*.md\", \"**\/*.json\") or type parameter (e.g., \"md\", \"js\", \"png\")
    - Output modes: \"content\" shows matching lines, \"files_with_matches\" shows only file paths (default), \"count\" shows match counts
    - Use Task tool for open-ended searches requiring multiple rounds
    - Pattern syntax: Uses ripgrep (not grep) - literal braces need escaping (use `interface\\{\\}` to find `interface{}` in Go code)
    - Multiline matching: By default patterns match within single lines only. For cross-line patterns like `struct \\{[\\s\\S]*?field`, use `multiline: true`
EOT;

    protected $parameters = [
        'pattern'     => [
            'type'        => 'string',
            'description' => 'The regular expression pattern to search for in file contents',
            'required'    => true,
        ],
        'path'        => [
            'type'        => 'string',
            'description' => 'File or directory to search in (rg PATH). Defaults to current working directory.',
        ],
        'glob'        => [
            'type'        => 'string',
            'description' => 'Glob pattern to filter files (e.g. "*.js", "*.{ts,tsx}") - maps to rg --glob',
        ],
        'output_mode' => [
            'type'        => 'string',
            'enum'        => ['content', 'files_with_matches', 'count'],
            'description' => 'Output mode: "content" shows matching lines (supports -A/-B/-C context, -n line numbers, head_limit), "files_with_matches" shows file paths (supports head_limit), "count" shows match counts (supports head_limit). Defaults to "files_with_matches".',
        ],
        '-B'          => [
            'type'        => 'number',
            'description' => 'Number of lines to show before each match (rg -B). Requires output_mode: "content", ignored otherwise.',
        ],
        '-A'          => [
            'type'        => 'number',
            'description' => 'Number of lines to show after each match (rg -A). Requires output_mode: "content", ignored otherwise.',
        ],
        '-C'          => [
            'type'        => 'number',
            'description' => 'Number of lines to show before and after each match (rg -C). Requires output_mode: "content", ignored otherwise.',
        ],
        '-n'          => [
            'type'        => 'boolean',
            'description' => 'Show line numbers in output (rg -n). Requires output_mode: "content", ignored otherwise.',
        ],
        '-i'          => [
            'type'        => 'boolean',
            'description' => 'Case insensitive search (rg -i)',
        ],
        'type'        => [
            'type'        => 'string',
            'description' => 'File type to search (rg --type). Common types: js, py, rust, go, java, etc. More efficient than include for standard file types.',
        ],
        'head_limit'  => [
            'type'        => 'number',
            'description' => 'Limit output to first N lines/entries, equivalent to "| head -N". Works across all output modes: content (limits output lines), files_with_matches (limits file paths), count (limits count entries). When unspecified, shows all results from ripgrep.',
        ],
        'multiline'   => [
            'type'        => 'boolean',
            'description' => 'Enable multiline mode where . matches newlines and patterns can span lines (rg -U --multiline-dotall). Default: false.',
        ],
    ];

    public function __construct(protected Workspace $workspace)
    {
    }

    protected function run(Args $args)
    {
        $pattern         = $args->get('pattern');
        $path            = $args->get('path', '.');
        $glob            = $args->get('glob', false);
        $outputMode      = $args->get('output_mode', 'files_with_matches');
        $contextBefore   = $args->get('-B', false);
        $contextAfter    = $args->get('-A', false);
        $contextAround   = $args->get('-C', false);
        $showLineNumbers = $args->get('-n', false);
        $caseInsensitive = $args->get('-i', false);
        $type            = $args->get('type', false);
        $headLimit       = $args->get('head_limit', false);
        $multiline       = $args->get('multiline', false);

        // 构建 ripgrep 命令参数
        $rgArgs = ['rg'];

        // 基础参数
        $rgArgs[] = '--hidden';
        $rgArgs[] = '--no-require-git';
        $rgArgs[] = '--no-ignore';
        $rgArgs[] = '--encoding';
        $rgArgs[] = 'utf-8';
        $rgArgs[] = '--crlf';

        // 大小写敏感性
        if ($caseInsensitive) {
            $rgArgs[] = '--ignore-case';
        } else {
            $rgArgs[] = '--case-sensitive';
        }

        // 多行模式
        if ($multiline) {
            $rgArgs[] = '-U';
            $rgArgs[] = '--multiline-dotall';
        }

        // 输出模式
        switch ($outputMode) {
            case 'content':
                // 显示匹配内容，支持上下文和行号
                if ($contextAround !== false) {
                    $rgArgs[] = '-C';
                    $rgArgs[] = (string) $contextAround;
                } else {
                    if ($contextBefore !== false) {
                        $rgArgs[] = '-B';
                        $rgArgs[] = (string) $contextBefore;
                    }
                    if ($contextAfter !== false) {
                        $rgArgs[] = '-A';
                        $rgArgs[] = (string) $contextAfter;
                    }
                }
                if ($showLineNumbers) {
                    $rgArgs[] = '-n';
                }
                break;

            case 'files_with_matches':
                $rgArgs[] = '-l';
                break;

            case 'count':
                $rgArgs[] = '-c';
                break;
        }

        // 文件类型过滤
        if ($type) {
            $rgArgs[] = '--type';
            $rgArgs[] = $type;
        }

        // Glob 模式过滤
        if ($glob) {
            $rgArgs[] = '--glob';
            $rgArgs[] = $glob;
        }

        // 正则表达式模式
        $rgArgs[] = '--regexp';
        $rgArgs[] = $pattern;

        // 搜索路径
        $rgArgs[] = $path;

        // 执行命令
        $process = new Process($rgArgs, $this->workspace->getRootDir());
        $process->run();

        $output = $process->getOutput();

        if (!$process->isSuccessful()) {
            $errorOutput = $process->getErrorOutput();
            if (empty($output) && !empty($errorOutput)) {
                return new Error("搜索失败: " . $errorOutput);
            }
            // 如果没有匹配结果，ripgrep 会返回非零退出码，但这是正常的
        }

        // 处理输出
        if (empty($output)) {
            return new Plain("未找到匹配的结果");
        }

        $lines = explode("\n", trim($output));

        // 应用 head_limit
        if ($headLimit && $headLimit > 0) {
            $lines = array_slice($lines, 0, $headLimit);
        }

        $result = implode("\n", $lines);

        return new Plain($result);
    }
}
