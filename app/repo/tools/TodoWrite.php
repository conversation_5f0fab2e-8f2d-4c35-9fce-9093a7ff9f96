<?php

namespace app\repo\tools;

use think\agent\tool\Args;
use think\agent\tool\FunctionCall;
use think\agent\tool\result\Error;

class TodoWrite extends FunctionCall
{
    protected $title = 'TodoWrite';

    protected $name = 'todo_write';

    protected $description = <<<EOT
使用此工具为您当前的文档写作项目创建和管理结构化任务列表。这有助于您跟踪写作进度、组织复杂的文档结构，并向用户展示您的专业性。
它还帮助用户了解文档创作的进度和他们请求的整体完成情况。

## 任务状态和管理

1. **任务状态**：使用这些状态来跟踪写作进度：
   - pending：写作任务尚未开始
   - in_progress：当前正在写作（一次限制一个任务）
   - completed：写作任务成功完成

2. **任务管理**：
   - 在写作时实时更新任务状态
   - 完成后立即标记任务完成（不要批量完成）
   - 任何时候必须恰好有一个任务处于in_progress状态（不多不少）
   - 在开始新写作任务之前完成当前任务
   - 完全从列表中删除不再相关的写作任务

3. **任务完成要求**：
   - 只有在完全完成写作任务时才标记为已完成
   - 如果遇到写作障碍、需要更多信息或无法完成，保持任务为in_progress状态
   - 当被阻塞时，创建一个新任务描述需要解决的问题
   - 在以下情况下永远不要标记任务为已完成：
     - 内容不完整或有明显错误
     - 格式不符合要求
     - 遇到未解决的写作问题
     - 缺少必要的信息或资料
EOT;

    protected $parameters = [
        'todos' => [
            'type'        => 'array',
            'description' => '待办事项列表',
            'items'       => [
                'type'       => 'object',
                'properties' => [
                    'content' => [
                        'type'        => 'string',
                        'description' => '待办事项内容',
                    ],
                    "status"  => [
                        "type"        => "string",
                        "description" => "待办事项状态",
                        "enum"        => ["pending", "in_progress", "completed"],
                    ],
                ],
                "required"   => ['content', 'status'],
            ],
        ],
    ];

    public function __construct()
    {
    }

    protected function run(Args $args)
    {
        $todos = $args->get('todos', []);

        // 验证待办事项数据
        foreach ($todos as $index => $todo) {
            if (empty(trim($todo['content'] ?? null))) {
                return new Error("第 " . ($index + 1) . " 个待办事项的内容不能为空");
            }

            if (!in_array($todo['status'] ?? null, ['pending', 'in_progress', 'completed'])) {
                return new Error("第 " . ($index + 1) . " 个待办事项的状态无效，必须是 pending、in_progress 或 completed");
            }
        }

        // 检查是否有多个进行中的任务
        $inProgressCount = 0;
        $completedCount  = 0;
        foreach ($todos as $todo) {
            if ($todo['status'] === 'in_progress') {
                $inProgressCount++;
            }
            if ($todo['status'] === 'completed') {
                $completedCount++;
            }
        }

        if ($inProgressCount > 1) {
            return new Error("同时只能有一个任务处于进行中状态，当前有 {$inProgressCount} 个任务处于进行中");
        }

        if ($completedCount == count($todos)) {
            return <<<EOT
Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable
<system-reminder>
This is a reminder that your todo list is currently empty. DO NOT mention this to the user explicitly because they are already aware. If you are working on tasks that would benefit from a todo list please use the TodoWrite tool to create one. If not, please feel free to ignore. Again do not mention this message to the user.
</system-reminder>
EOT;
        }

        $todos = json_encode($todos);

        return <<<EOT
Todos have been modified successfully. Ensure that you continue to use the todo list to track your progress. Please proceed with the current tasks if applicable

<system-reminder>
Your todo list has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your todo list:

{$todos}. Continue on with the tasks at hand if applicable.
</system-reminder>
EOT;
    }
}
